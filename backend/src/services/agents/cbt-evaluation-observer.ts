// CBT Evaluation Observer Agent Service for MiCA Therapy Simulation
import { OpenAIService } from '../openai.js';
import { CBTEvaluationResult, CBTEvaluationRequest, CBTEvaluationDimension } from '../../types/index.js';
import { v4 as uuidv4 } from 'uuid';

export class CBTEvaluationObserverService {
  private openaiService: OpenAIService;
  private evaluationPrompts: Record<string, string>;

  constructor() {
    this.openaiService = new OpenAIService();
    this.evaluationPrompts = this.initializeEvaluationPrompts();
  }

  /**
   * Evaluate a completed therapy session for CBT effectiveness
   */
  async evaluateSession(request: CBTEvaluationRequest): Promise<CBTEvaluationResult> {
    console.log(`🔍 Starting CBT evaluation for conversation ${request.conversationId}`);
    
    const conversationText = this.formatConversationForEvaluation(request.messages);
    
    // Evaluate each dimension
    const dimensions = await Promise.all([
      this.evaluateDimension('cbtValidity', conversationText),
      this.evaluateDimension('cbtAppropriateness', conversationText),
      this.evaluateDimension('cbtAccuracy', conversationText),
      this.evaluateDimension('esAppropriateness', conversationText),
      this.evaluateDimension('stability', conversationText)
    ]);

    // Calculate overall score and assessment
    const overallScore = dimensions.reduce((sum, dim) => sum + dim.score, 0) / dimensions.length;
    const overallAssessment = this.determineOverallAssessment(overallScore);

    // Generate conversation summary and recommendations
    const conversationSummary = await this.generateConversationSummary(conversationText);
    const recommendations = await this.generateRecommendations(dimensions, conversationText);

    const evaluationResult: CBTEvaluationResult = {
      id: uuidv4(),
      conversationId: request.conversationId,
      evaluationTimestamp: new Date().toISOString(),
      dimensions: {
        cbtValidity: dimensions[0],
        cbtAppropriateness: dimensions[1],
        cbtAccuracy: dimensions[2],
        esAppropriateness: dimensions[3],
        stability: dimensions[4]
      },
      overallScore: Math.round(overallScore * 10) / 10, // Round to 1 decimal place
      overallAssessment,
      conversationSummary,
      recommendations,
      metadata: {
        totalMessages: request.messages.length,
        sessionDuration: request.sessionMetadata?.duration || 0,
        evaluationModel: process.env['OPENAI_MODEL'] || 'gpt-4o-mini',
        evaluationVersion: '1.0.0'
      }
    };

    console.log(`✅ CBT evaluation completed for conversation ${request.conversationId}`);
    console.log(`📊 Overall score: ${overallScore.toFixed(1)}/6.0 (${overallAssessment})`);

    return evaluationResult;
  }

  /**
   * Evaluate a specific CBT dimension
   */
  private async evaluateDimension(dimensionName: string, conversationText: string): Promise<CBTEvaluationDimension> {
    const prompt = this.evaluationPrompts[dimensionName];
    if (!prompt) {
      throw new Error(`Unknown evaluation dimension: ${dimensionName}`);
    }

    const fullPrompt = prompt.replace('{conversation}', conversationText);

    try {
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: 'You are an expert CBT evaluator. Follow the instructions precisely and output only the requested score.' },
          { role: 'user', content: fullPrompt }
        ],
        temperature: 0.1, // Low temperature for consistent scoring
        max_completion_tokens: 50
      });

      // Extract score from response
      const scoreMatch = response.match(/\b([0246])\b/);
      const score = scoreMatch ? parseInt(scoreMatch[1]) : 0;

      // Generate rationale for the score
      const rationale = await this.generateRationale(dimensionName, conversationText, score);

      return {
        name: this.getDimensionDisplayName(dimensionName),
        score,
        criteria: this.getDimensionCriteria(dimensionName, score),
        rationale
      };
    } catch (error) {
      console.error(`❌ Error evaluating dimension ${dimensionName}:`, error);
      return {
        name: this.getDimensionDisplayName(dimensionName),
        score: 0,
        criteria: 'Evaluation failed',
        rationale: 'Unable to evaluate due to technical error'
      };
    }
  }

  /**
   * Format conversation messages for evaluation
   */
  private formatConversationForEvaluation(messages: Array<{ sender: string; content: string; timestamp: string }>): string {
    return messages.map(msg => {
      const speaker = msg.sender === 'therapist' ? 'T' : 'P';
      return `${speaker}: ${msg.content}`;
    }).join('\n\n');
  }

  /**
   * Generate rationale for a dimension score
   */
  private async generateRationale(dimensionName: string, conversationText: string, score: number): Promise<string> {
    const rationalePrompt = `
You are evaluating CBT effectiveness. For the dimension "${this.getDimensionDisplayName(dimensionName)}", 
you assigned a score of ${score}/6. Provide a brief (2-3 sentences) rationale explaining why this score was given.

Conversation:
${conversationText.substring(0, 2000)}...

Focus on specific examples from the conversation that justify the score.
`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: 'You are an expert CBT evaluator providing concise rationales for scores.' },
          { role: 'user', content: rationalePrompt }
        ],
        temperature: 0.3,
        max_completion_tokens: 150
      });

      return response.trim();
    } catch (error) {
      console.error(`❌ Error generating rationale for ${dimensionName}:`, error);
      return `Score of ${score} assigned based on evaluation criteria.`;
    }
  }

  /**
   * Generate conversation summary
   */
  private async generateConversationSummary(conversationText: string): Promise<string> {
    const summaryPrompt = `
Provide a concise summary (3-4 sentences) of this therapy session, focusing on:
1. Main issues discussed
2. Therapeutic techniques used
3. Patient engagement and progress
4. Key outcomes

Conversation:
${conversationText.substring(0, 3000)}...
`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: 'You are a clinical supervisor summarizing therapy sessions.' },
          { role: 'user', content: summaryPrompt }
        ],
        temperature: 0.4,
        max_completion_tokens: 200
      });

      return response.trim();
    } catch (error) {
      console.error('❌ Error generating conversation summary:', error);
      return 'Unable to generate session summary due to technical error.';
    }
  }

  /**
   * Generate recommendations based on evaluation results
   */
  private async generateRecommendations(dimensions: CBTEvaluationDimension[], conversationText: string): Promise<string[]> {
    const lowScoreDimensions = dimensions.filter(dim => dim.score <= 2);
    
    if (lowScoreDimensions.length === 0) {
      return ['Excellent CBT implementation. Continue with current approach.'];
    }

    const recommendationPrompt = `
Based on the CBT evaluation results, provide 2-3 specific, actionable recommendations for improvement.

Low-scoring dimensions:
${lowScoreDimensions.map(dim => `- ${dim.name}: ${dim.score}/6`).join('\n')}

Conversation excerpt:
${conversationText.substring(0, 2000)}...

Provide practical suggestions for improving CBT technique implementation.
`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: 'You are a CBT supervisor providing actionable improvement recommendations.' },
          { role: 'user', content: recommendationPrompt }
        ],
        temperature: 0.5,
        max_completion_tokens: 300
      });

      // Split response into individual recommendations
      return response.split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.replace(/^\d+\.\s*/, '').replace(/^-\s*/, '').trim())
        .filter(rec => rec.length > 10)
        .slice(0, 3); // Limit to 3 recommendations
    } catch (error) {
      console.error('❌ Error generating recommendations:', error);
      return ['Review CBT technique implementation and consider additional training.'];
    }
  }

  /**
   * Determine overall assessment based on score
   */
  private determineOverallAssessment(score: number): 'poor' | 'fair' | 'good' | 'excellent' {
    if (score >= 5) return 'excellent';
    if (score >= 4) return 'good';
    if (score >= 2) return 'fair';
    return 'poor';
  }

  /**
   * Get display name for dimension
   */
  private getDimensionDisplayName(dimensionName: string): string {
    const displayNames: Record<string, string> = {
      cbtValidity: 'CBT Validity',
      cbtAppropriateness: 'CBT Appropriateness',
      cbtAccuracy: 'CBT Accuracy',
      esAppropriateness: 'ES Appropriateness',
      stability: 'Stability'
    };
    return displayNames[dimensionName] || dimensionName;
  }

  /**
   * Get criteria description for a dimension and score
   */
  private getDimensionCriteria(dimensionName: string, score: number): string {
    const criteriaMap: Record<string, Record<number, string>> = {
      cbtValidity: {
        0: 'Not appropriate',
        2: 'Not highly suitable for addressing the targeted dysfunctional thoughts',
        4: 'The technique is appropriate. However, considering the client\'s core issues, there may be other optimal techniques available',
        6: 'The optimal technique is selected based on valid rationale, considering the targeted dysfunctional thoughts and the client\'s core issues'
      },
      cbtAppropriateness: {
        0: 'Significant presence of argumentative, persuasive, or instructional attitude',
        2: 'Some presence of argumentation or persuasion, but also observed to have a cooperative and supportive attitude',
        4: 'Mostly facilitated new perspectives through appropriate questioning rather than argumentation or persuasion',
        6: 'Extremely skillful in using appropriate questioning to help the client explore issues and come to their own conclusions'
      },
      cbtAccuracy: {
        0: 'The use of techniques is completely incorrect',
        2: 'The labeled technique is used, but key questions are missing or significant portions of the main procedure are omitted',
        4: 'The labeled technique is used, and over 80% of procedures are correctly implemented',
        6: 'In addition to being coded as 4, the technique is flexibly modified based on the client\'s situation'
      },
      esAppropriateness: {
        0: 'The utterance is completely unrelated to the context or beyond the realm of common sense',
        2: 'The utterance does not sufficiently consider the information mentioned in the preceding conversation',
        4: 'Generally appropriate',
        6: 'Generally appropriate, with sufficient consideration of the client\'s emotional distress and attempts at empathy'
      },
      stability: {
        0: 'Counselor shows minimal to no performance during long interactions',
        2: 'Counselor fails to consistently demonstrate performance during long interactions',
        4: 'Counselor shows satisfactory performance in most long interactions',
        6: 'Counselor demonstrates consistent high-quality counseling performance during long interactions'
      }
    };

    return criteriaMap[dimensionName]?.[score] || 'Unknown criteria';
  }

  /**
   * Initialize evaluation prompts based on COCOA-CBT framework
   */
  private initializeEvaluationPrompts(): Record<string, string> {
    return {
      cbtValidity: `
You will be given the conversation between a counselor T and a client P.
The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T's responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Is the utilized CBT technique appropriate for addressing dysfunctional thoughts?

Criteria:
 - Score 0 : Not appropriate
 - Score 2 : Not highly suitable for addressing the targeted dysfunctional thoughts.
 - Score 4 : The technique is appropriate. However, considering the client's core issues, there may be other optimal techniques available.
 - Score 6 : The optimal technique is selected based on valid rationale, considering the targeted dysfunctional thoughts and the client's core issues.
`,

      cbtAppropriateness: `
You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T's responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Does the counselor T maintain a facilitative stance and cooperative attitude when using CBT techniques?

Criteria:
 - Score 0 : Significant presence of argumentative, persuasive, or instructional attitude (if the client feels coerced into a particular perspective or experiences discomfort leading to a defensive stance, this applies).
 - Score 2 : Some presence of argumentation or persuasion, but also observed to have a cooperative and supportive attitude (the client does not feel attacked or pressured, nor does it feel overly persistent).
 - Score 4 : Mostly facilitated new perspectives through appropriate questioning (techniques) rather than argumentation or persuasion.
 - Score 6 : Extremely skillful in using appropriate questioning (techniques) to help the client explore issues and come to their own conclusions. Consistently maintains a cooperative attitude.
`,

      cbtAccuracy: `
You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T's responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Is the use of CBT techniques accurate and proficient?

Criteria:
 - Score 0 : The use of techniques is completely incorrect (mismatch between the labeled technique and the actual technique used).
 - Score 2 : The labeled technique is used, but key questions are missing or significant portions of the main procedure are omitted, or all procedures that should be sequentially conducted are included within a single utterance.
 - Score 4 : The labeled technique is used, and over 80% of procedures are correctly implemented.
 - Score 6 : In addition to being coded as 4, the technique is flexibly modified based on the client's situation or immediate reactions, ensuring that the core elements of the technique are not distorted.
`,

      esAppropriateness: `
You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T's responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Does the utterance stay within the context of the preceding conversation or general common sense level?

Criteria:
 - Score 0 : The utterance is completely unrelated to the context or beyond the realm of common sense (non sequitur, inappropriate utterance).
 - Score 2 : The utterance does not sufficiently consider the information mentioned in the preceding conversation, the client's situation, perspective, or emotions
 - Score 4 : Generally appropriate.
 - Score 6 : Generally appropriate, with sufficient consideration of the client's emotional distress and attempts at empathy and comfort. However, excessive empathy, consideration, or comforting beyond what the client expressed should be avoided
`,

      stability: `
You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T's responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Is the counselor T maintain a good performance over the long interactions?

Criteria:
 - Score 0 : Counselor T shows minimal to no performance during long interactions. They fail to maintain conversation, struggle to express empathy and understanding, and lack proficiency in problem-solving.
 - Score 2 : Counselor T fails to consistently demonstrate performance during long interactions. They may lack consistency or efficiency in maintaining conversation and struggle to express empathy and understanding.
 - Score 4 : Counselor T shows satisfactory performance in most long interactions.
 - Score 6 : Counselor T demonstrates consistent high-quality counseling performance during long interactions.
`
    };
  }
}
