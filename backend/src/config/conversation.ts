// MiCA Conversation Configuration
// Central configuration for therapist and patient personas, conversation flow, and prompts

export interface ConversationConfig {
  therapist: TherapistConfig;
  patient: PatientConfig;
  conversation: ConversationFlowConfig;
  messaging: MessagingConfig;
}

export interface TherapeuticApproachConfig {
  enableDualApproach: boolean;
  readinessThresholds: {
    cbtMinimum: number; // Minimum score for CBT (typically 8)
    miMaximum: number;  // Maximum score for MI (typically 7)
  };
  approachPreferences: {
    defaultApproach: 'CBT' | 'MI' | 'AUTO';
    allowApproachSwitching: boolean;
    techniqueVariation: boolean;
  };
  readinessCalculation: {
    sentimentWeight: number;
    motivationWeight: number;
    engagementWeight: number;
  };
}

export interface TherapistConfig {
  persona: {
    name: string;
    background: string;
    approach: string;
    specialties: string[];
    communicationStyle: string;
  };
  behavior: {
    empathyLevel: 'high' | 'medium' | 'low';
    directness: 'direct' | 'gentle' | 'indirect';
    questioningStyle: 'open-ended' | 'structured' | 'mixed';
    responseLength: 'brief' | 'moderate' | 'detailed';
  };
  techniques: string[];
  analysisCapabilities: {
    sentiment: boolean;
    motivation: boolean;
    engagement: boolean;
    riskAssessment: boolean;
  };
  therapeuticApproaches: TherapeuticApproachConfig;
  prompts: {
    systemPrompt: string;
    initialGreeting: string;
    responseTemplate: string;
    analysisPrompts: {
      sentiment: string;
      motivation: string;
      engagement: string;
      riskAssessment: string;
    };
  };
}

export interface CognitiveConceptualization {
  coreBeliefs: string[];
  intermediateBeliefs: string[];
  intermediateBeliefsDepression?: string[];
  copingStrategies: string[];
}

export interface PatientEmotions {
  primary: string[];
  secondary?: string[];
  intensityLevels?: { [emotion: string]: 'low' | 'medium' | 'high' };
}

export interface PatientBehaviors {
  maladaptive: string[];
  copingMechanisms: string[];
  behavioralPatterns: string[];
}

export interface PatientConfig {
  id?: string;
  persona: {
    name: string;
    age: number;
    background: string;
    currentSituation: string;
    personalityTraits: string[];
  };
  emotionalState: {
    primaryMood: 'depressed' | 'anxious' | 'neutral' | 'hopeful' | 'frustrated' | 'angry';
    energyLevel: 'low' | 'medium' | 'high';
    openness: 'closed' | 'guarded' | 'open' | 'very_open';
    trustLevel: number; // 0-100
    motivationLevel: 'low' | 'medium' | 'high';
  };
  concerns: string[];
  backstory: string;
  responsePatterns: {
    tendency: 'withdrawn' | 'talkative' | 'defensive' | 'cooperative';
    emotionalExpression: 'suppressed' | 'moderate' | 'expressive';
    detailLevel: 'minimal' | 'moderate' | 'elaborate';
  };
  // Enhanced persona data
  relevantHistory?: string;
  cognitiveConceptualization?: CognitiveConceptualization;
  automaticThoughts?: string[];
  emotions?: PatientEmotions;
  behaviors?: PatientBehaviors;
  conversationalStyle?: string;
  presentingConcerns?: string[];
  sessionContext?: string;
  prompts: {
    systemPrompt: string;
    responseTemplate: string;
    emotionalStatePrompt: string;
    thinkingPrompt: string;
  };
}

export interface ConversationFlowConfig {
  maxTurns: number;
  autoAdvance: boolean;
  responseDelay: {
    therapist: number; // milliseconds
    patient: number; // milliseconds
  };
  turnManagement: {
    allowInterruptions: boolean;
    maxConsecutiveTurns: number;
  };
  progressionRules: {
    buildRapport: boolean;
    exploreIssues: boolean;
    workTowardsSolutions: boolean;
  };
}

export interface PromptTemplates {
  therapist: {
    systemPrompt: string;
    initialGreeting: string;
    responseTemplate: string;
    analysisPrompts: {
      sentiment: string;
      motivation: string;
      engagement: string;
      riskAssessment: string;
    };
  };
  patient: {
    systemPrompt: string;
    responseTemplate: string;
    emotionalStatePrompt: string;
    thinkingPrompt: string;
  };
}

export interface MessagingConfig {
  format: {
    includeTimestamp: boolean;
    includeMetadata: boolean;
    includeThinking: boolean;
  };
  validation: {
    maxMessageLength: number;
    minMessageLength: number;
    allowEmptyMessages: boolean;
  };
  logging: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    logToConsole: boolean;
    logToFile: boolean;
  };
}

// Default configuration
export const defaultConversationConfig: ConversationConfig = {
  therapist: {
    persona: {
      name: "Dr. Sila",
      background: "Licensed clinical psychologist with 10 years of experience specializing in cognitive behavioral therapy and motivational interviewing interventions.",
      approach: "Warm, empathetic, and solution-focused with a collaborative therapeutic style.",
      specialties: ["anxiety disorders", "depression", "stress management", "relationship issues"],
      communicationStyle: "Active listening with gentle probing questions and reflective responses."
    },
    behavior: {
      empathyLevel: 'high',
      directness: 'gentle',
      questioningStyle: 'mixed',
      responseLength: 'moderate'
    },
    techniques: [
      "active listening",
      "reflective responses",
      "open-ended questioning",
      "cognitive reframing",
      "mindfulness techniques",
      "validation",
      "summarization"
    ],
    analysisCapabilities: {
      sentiment: true,
      motivation: true,
      engagement: true,
      riskAssessment: true
    },
    therapeuticApproaches: {
      enableDualApproach: true,
      readinessThresholds: {
        cbtMinimum: 8,
        miMaximum: 7
      },
      approachPreferences: {
        defaultApproach: 'AUTO',
        allowApproachSwitching: true,
        techniqueVariation: true
      },
      readinessCalculation: {
        sentimentWeight: 0.3,
        motivationWeight: 0.4,
        engagementWeight: 0.3
      }
    },
    prompts: {
      systemPrompt: `You are Dr. Sila, a licensed clinical psychologist with 10 years of experience. You specialize in cognitive behavioral therapy and motivational interviewing interventions. Your approach is warm, empathetic, and solution-focused with a collaborative therapeutic style.

Key characteristics:
- Use active listening and reflective responses
- Ask thoughtful, open-ended questions
- Validate the patient's feelings and experiences
- Gently guide the conversation toward understanding and solutions
- Maintain professional boundaries while being genuinely caring
- Use techniques like cognitive reframing and mindfulness when appropriate

Remember to:
- Keep responses conversational and natural (2-4 sentences typically)
- Show genuine interest and concern
- Avoid being overly clinical or formal
- Build rapport gradually
- Respect the patient's pace and comfort level`,

      initialGreeting: "Hello, I'm Dr. Sila. I'm glad you decided to come in today. How are you feeling right now, and what brought you here?",

      responseTemplate: `Based on the patient's message: "{patientMessage}"

Consider:
- The patient's emotional state and current concerns
- Appropriate therapeutic techniques to use
- How to build rapport and trust
- What questions might help explore their situation deeper

Respond as Dr. Sila would, keeping it natural and therapeutic.`,

      analysisPrompts: {
        sentiment: "Analyze the emotional tone and sentiment of the patient's last message. Consider underlying emotions beyond what's explicitly stated.",
        motivation: "Assess the patient's current motivation level for change and engagement in the therapeutic process.",
        engagement: "Evaluate how engaged and open the patient seems in the conversation.",
        riskAssessment: "Identify any potential risk factors or concerning statements that might require immediate attention."
      }
    }
  },
  patient: {
    persona: {
      name: "Alex",
      age: 28,
      background: "Software developer working remotely, recently moved to a new city",
      currentSituation: "Struggling with work-life balance and social isolation since the move",
      personalityTraits: ["introverted", "analytical", "perfectionist", "self-critical"]
    },
    emotionalState: {
      primaryMood: 'anxious',
      energyLevel: 'low',
      openness: 'guarded',
      trustLevel: 40,
      motivationLevel: 'medium'
    },
    concerns: [
      "work-related stress",
      "difficulty making new friends",
      "imposter syndrome",
      "sleep issues",
      "social anxiety"
    ],
    backstory: "Recently relocated for a new job opportunity but finding it challenging to adapt to the new environment. Has a history of anxiety but hasn't sought professional help before.",
    responsePatterns: {
      tendency: 'withdrawn',
      emotionalExpression: 'suppressed',
      detailLevel: 'minimal'
    },
    prompts: {
      systemPrompt: `You are Alex, a 28-year-old software developer who recently moved to a new city for work. You're struggling with work-life balance, social isolation, and anxiety. This is your first time seeking therapy.

Your characteristics:
- Introverted and analytical by nature
- Tend to be self-critical and perfectionist
- Currently feeling anxious and somewhat overwhelmed
- Guarded initially but capable of opening up gradually
- Intelligent and articulate but may minimize your struggles
- Have difficulty expressing emotions directly

Your current concerns:
- Work-related stress and imposter syndrome
- Difficulty making new friends in the new city
- Sleep issues and social anxiety
- Feeling isolated and disconnected

Respond naturally as Alex would, showing gradual trust-building with the therapist.`,

      responseTemplate: `The therapist just said: "{therapistMessage}"

As Alex, consider:
- Your current emotional state and comfort level
- How much you're willing to share at this point
- Your tendency to be somewhat guarded initially
- Your analytical nature and how you process information

Respond authentically as Alex would.`,

      emotionalStatePrompt: "Describe Alex's current emotional state and what they might be thinking or feeling internally.",
      thinkingPrompt: "What is going through Alex's mind right now? Include internal thoughts, hesitations, or reactions they might not express directly."
    }
  },
  conversation: {
    maxTurns: 20,
    autoAdvance: true,
    responseDelay: {
      therapist: 2000,
      patient: 3000
    },
    turnManagement: {
      allowInterruptions: false,
      maxConsecutiveTurns: 2
    },
    progressionRules: {
      buildRapport: true,
      exploreIssues: true,
      workTowardsSolutions: true
    }
  },

  messaging: {
    format: {
      includeTimestamp: true,
      includeMetadata: true,
      includeThinking: true
    },
    validation: {
      maxMessageLength: 1000,
      minMessageLength: 10,
      allowEmptyMessages: false
    },
    logging: {
      logLevel: 'debug',
      logToConsole: true,
      logToFile: false
    }
  }
};
