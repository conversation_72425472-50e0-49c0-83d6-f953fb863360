<script lang="ts">
  import { onMount } from 'svelte';
  import ThemeToggle from '$components/ui/ThemeToggle.svelte';
  import PersonaSelector from '$components/PersonaSelector.svelte';
  import EvaluationDisplay from '$components/EvaluationDisplay.svelte';
  import type { CBTEvaluationResult } from '../types/index.js';

  let wsConnection: WebSocket | null = null;
  let connectionStatus = 'Disconnected';
  let messages: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
    thinking?: string;
    metadata?: any;
  }> = [];
  let therapistThoughts: Array<{ content: string; timestamp: string }> = [];
  let patientThoughts: Array<{ content: string; timestamp: string }> = [];

  // Profile data - will be fetched from API
  let therapistProfile = {
    name: "Loading...",
    credentials: "",
    specialization: "",
    approach: "",
    experience: "",
    sessions: ""
  };

  let patientProfile = {
    name: "No patient selected",
    age: "",
    sessionHistory: "",
    background: "",
    traits: "",
    lastSession: ""
  };

  // Selected persona details for display
  let selectedPersonaDetails: any = null;

  // Analytics data - tracks pre and post conversation metrics
  let preConversationAnalytics = {
    sentiment: null,
    motivationLevel: null,
    engagementLevel: null
  };

  let postConversationAnalytics = {
    sentiment: null,
    motivationLevel: null,
    engagementLevel: null
  };

  // Dummy messages for testing - updated to match backend structure
  // messages = [
  //   {
  //     sender: 'therapist',
  //     content: 'Hello, I\'m Dr. Sila. I\'m glad you\'re here today. How are you feeling right now?',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'Patient seems anxious. Need to establish rapport and create safe space.',
  //     metadata: {
  //       confidence: 0.9,
  //       processingTime: 1250,
  //       patientAnalysis: {
  //         sentiment: 'neutral',
  //         sentimentIntensity: 'medium',
  //         motivationLevel: 'medium',
  //         motivationType: 'mixed',
  //         engagementLevel: 'medium',
  //         engagementPatterns: ['hesitant', 'observant'],
  //         readinessScore: {
  //           score: 6,
  //           recommendedApproach: 'MI',
  //           reasoning: 'Patient shows moderate engagement but appears guarded. MI approach recommended to build motivation.',
  //           indicators: {
  //             positive: ['present', 'responsive'],
  //             negative: ['guarded', 'uncertain']
  //           }
  //         }
  //       },
  //       therapeuticApproach: {
  //         name: 'Motivational Interviewing',
  //         selectedTechnique: {
  //           name: 'Open-ended Questions',
  //           description: 'Using open-ended questions to encourage patient expression and exploration'
  //         }
  //       }
  //     }
  //   },
  //   {
  //     sender: 'patient',
  //     content: 'I\'m... okay, I guess. A bit nervous about being here.',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'This feels awkward. Not sure what to expect. Should I trust this person?',
  //     metadata: {
  //       confidence: 0.8,
  //       processingTime: 950
  //     }
  //   }
  // ];
  // therapistThoughts = [
  //   {
  //     content: 'Patient seems anxious. Need to establish rapport and create safe space. Using open-ended questions to encourage expression.',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  // patientThoughts = [
  //   {
  //     content: 'This feels awkward. Not sure what to expect. Should I trust this person? Maybe I should give this a chance.',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  
  // Configuration state
  let maxTurns = 20;
  let conversationActive = false;
  let conversationId: string | null = null;
  let selectedPersonaId: string | null = null;
  let showPersonaSelector = false;
  let showPatientDetailsModal = false;

  // Evaluation state
  let currentEvaluation: CBTEvaluationResult | null = null;
  let evaluationLoading = false;
  let evaluationError: string | null = null;
  let showEvaluation = false;
  
  onMount(() => {
    connectWebSocket();
    fetchTherapistProfile();

    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  });

  async function fetchTherapistProfile() {
    try {
      const response = await fetch('/api/therapist/profile');
      const result = await response.json();
      if (result.success) {
        therapistProfile = {
          name: result.data.name,
          credentials: result.data.credentials,
          specialization: result.data.specialization,
          approach: result.data.approach,
          experience: result.data.experience,
          sessions: result.data.sessions
        };
      }
    } catch (error) {
      console.error('Error fetching therapist profile:', error);
      // Keep default loading state if fetch fails
    }
  }

  function connectWebSocket() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3000';
    wsConnection = new WebSocket(`${wsUrl}/ws`);
    
    wsConnection.onopen = () => {
      connectionStatus = 'Connected';
      console.log('🔌 WebSocket connected successfully');
    };

    wsConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📨 Received WebSocket message:', data);

        handleWebSocketMessage(data);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };
    
    wsConnection.onclose = () => {
      connectionStatus = 'Disconnected';
      console.log('WebSocket disconnected');
    };
    
    wsConnection.onerror = (error) => {
      connectionStatus = 'error';
      console.error('WebSocket error:', error);
    };
  }
  
  function handleWebSocketMessage(data: any) {
    console.log(`🔄 Handling message type: ${data.type}`);

    switch (data.type) {
      case 'welcome':
        console.log('👋 Welcome message received');
        break;

      case 'conversation_created':
        console.log('✅ Conversation created:', data.data);
        conversationId = data.data.conversationId;
        break;

      case 'conversation_started':
        console.log('🚀 Conversation started:', data.data);
        break;

      case 'conversation_message':
        console.log('💬 Conversation message received:', data.data);
        handleConversationMessage(data.data);
        break;

      case 'conversation_ended':
        console.log('🏁 Conversation ended:', data.data);
        conversationActive = false;
        evaluationLoading = true;
        evaluationError = null;
        showEvaluation = true;
        console.log('messages:', messages);
        break;

      case 'conversation_paused':
        console.log('⏸️ Conversation paused');
        break;

      case 'conversation_resumed':
        console.log('▶️ Conversation resumed');
        break;

      case 'conversation_cleared':
        console.log('🗑️ Conversation cleared');
        messages = [];
        therapistThoughts = [];
        patientThoughts = [];
        currentEvaluation = null;
        evaluationLoading = false;
        evaluationError = null;
        showEvaluation = false;
        break;

      case 'evaluation_completed':
        console.log('✅ Evaluation completed:', data.data);
        currentEvaluation = data.data.evaluation;
        evaluationLoading = false;
        evaluationError = null;
        break;

      case 'evaluation_error':
        console.error('❌ Evaluation error:', data.data);
        evaluationLoading = false;
        evaluationError = data.data.message || 'Evaluation failed';
        break;

      case 'error':
        console.error('❌ Server error:', data.message);
        break;

      default:
        console.warn('⚠️ Unknown message type:', data.type);
        // For backward compatibility, treat unknown messages as general messages
        if (data.message) {
          messages = [...messages, {
            sender: 'therapist', // Default sender
            content: data.message,
            timestamp: data.timestamp || new Date().toISOString()
          }];
        }
    }
  }

  function handleConversationMessage(messageData: any) {
    console.log('📝 Processing conversation message:', messageData);

    if (messageData.message) {
      // Add conversation message
      messages = [...messages, {
        sender: messageData.message.sender,
        content: messageData.message.content,
        timestamp: messageData.message.timestamp,
        thinking: messageData.thinking?.content,
        metadata: messageData.metadata
      }];

      // Update analytics based on therapist's analysis of patient
      if (messageData.message.sender === 'therapist' && messageData.metadata?.patientAnalysis) {
        const analysis = messageData.metadata.patientAnalysis;

        // Set pre-conversation analytics from second therapist message
        if (messages.length === 3) {
          preConversationAnalytics = {
            sentiment: analysis.sentiment,
            motivationLevel: analysis.motivationLevel,
            engagementLevel: analysis.engagementLevel
          };
        }

        // Always update post-conversation analytics with latest analysis
        postConversationAnalytics = {
          sentiment: analysis.sentiment,
          motivationLevel: analysis.motivationLevel,
          engagementLevel: analysis.engagementLevel
        };
      }

      console.log(`💭 ${messageData.message.sender} said: "${messageData.message.content}"`);
    }

    if (messageData.thinking) {
      // Add thinking to appropriate array
      const thought = {
        content: messageData.thinking.content,
        timestamp: messageData.thinking.timestamp
      };

      if (messageData.thinking.agent === 'therapist') {
        therapistThoughts = [...therapistThoughts, thought];
        console.log(`🧠 Therapist thinking: "${thought.content}"`);
      } else if (messageData.thinking.agent === 'patient') {
        patientThoughts = [...patientThoughts, thought];
        console.log(`💭 Patient thinking: "${thought.content}"`);
      }
    }
  }

  async function handlePersonaSelect(personaId: string) {
    selectedPersonaId = personaId;
    showPersonaSelector = false;
    console.log('🎭 Selected persona:', personaId);

    // Fetch persona details for display
    try {
      const response = await fetch(`/api/personas/${personaId}`);
      const result = await response.json();
      if (result.success) {
        selectedPersonaDetails = result.data;
        // Update patient profile display
        patientProfile = {
          name: result.data.name,
          age: result.data.age.toString(),
          sessionHistory: result.data.sessionContext,
          background: result.data.background,
          traits: result.data.conversationalStyle,
          lastSession: "Starting new session"
        };
      }
    } catch (error) {
      console.error('Error fetching persona details:', error);
    }
  }

  function startConversation() {
    console.log('🚀 Starting conversation...');
    console.log(`⚙️ Configuration: maxTurns=${maxTurns}, personaId=${selectedPersonaId}`);

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      // Clear evaluation data
      currentEvaluation = null;
      evaluationLoading = false;
      evaluationError = null;
      showEvaluation = false;

      conversationActive = true;

      const message = {
        type: 'start_conversation',
        config: {
          maxTurns,
          personaId: selectedPersonaId
        }
      };

      console.log('📤 Sending start conversation message:', message);
      wsConnection.send(JSON.stringify(message));
    } else {
      console.error('❌ WebSocket not connected');
    }
  }

  function clearConversation() {
    console.log('🗑️ Clearing conversation...');

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN && conversationId) {
      wsConnection.send(JSON.stringify({
        type: 'clear_conversation'
      }));
    }

    // Clear local state
    messages = [];
    therapistThoughts = [];
    patientThoughts = [];
    conversationActive = false;
    conversationId = null;

    // Reset analytics
    preConversationAnalytics = {
      sentiment: null,
      motivationLevel: null,
      engagementLevel: null
    };
    postConversationAnalytics = {
      sentiment: null,
      motivationLevel: null,
      engagementLevel: null
    };
  }
</script>

<svelte:head>
  <title>MiCA</title>
</svelte:head>

<div class="min-h-screen bg-neutral-50 dark:bg-neutral-900 theme-transition">
  <!-- Header -->
  <header class="bg-white dark:bg-neutral-800 shadow-sm border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <h1 class="text-2xl font-bold text-neutral-900 dark:text-neutral-100 theme-transition">MiCA</h1>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Theme Toggle -->
          <ThemeToggle variant="button" size="md" />

          <!-- WebSocket Status -->
          <div class="flex items-center space-x-2">
            <span class="text-sm text-neutral-600 dark:text-neutral-400 theme-transition">WebSocket Status:</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {
              connectionStatus === 'Connected' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
              connectionStatus === 'Error' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
              'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
            } theme-transition">
              {connectionStatus}
            </span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Configuration Panel -->
  <div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <label for="maxTurns" class="label">Max Turns:</label>
            <input
              id="maxTurns"
              type="number"
              bind:value={maxTurns}
              min="1"
              max="100"
              class="input w-20"
              disabled={conversationActive}
            />
          </div>
          <div class="flex items-center space-x-2">
            <button
              on:click={() => showPersonaSelector = !showPersonaSelector}
              disabled={conversationActive}
              class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {selectedPersonaId ? 'Change Persona' : 'Select Persona'}
            </button>
            {#if selectedPersonaId}
              <span class="text-sm text-neutral-600 dark:text-neutral-400">
                Persona selected
              </span>
            {/if}
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button
            on:click={startConversation}
            disabled={conversationActive || connectionStatus !== 'Connected'}
            class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {conversationActive ? 'Running...' : 'Start'}
          </button>
          <button
            on:click={clearConversation}
            disabled={conversationActive}
            class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Persona Selector Modal -->
  {#if showPersonaSelector}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white dark:bg-neutral-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
              Select Patient Persona
            </h2>
            <button
              on:click={() => showPersonaSelector = false}
              class="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <PersonaSelector
            {selectedPersonaId}
            onPersonaSelect={handlePersonaSelect}
          />
        </div>
      </div>
    </div>
  {/if}

  <!-- Patient Details Modal -->
  {#if showPatientDetailsModal && selectedPersonaDetails}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-neutral-100 dark:bg-neutral-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-semibold text-neutral-900 dark:text-neutral-100">
              Patient Details: {selectedPersonaDetails.name}
            </h2>
            <button
              on:click={() => showPatientDetailsModal = false}
              class="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Patient Overview -->
          <div class="mb-6 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-2">Basic Information</h3>
                <div class="space-y-1 text-sm text-neutral-800 dark:text-neutral-200">
                  <div><span class="font-semibold">Name:</span> {selectedPersonaDetails.name}</div>
                  <div><span class="font-semibold">Age:</span> {selectedPersonaDetails.age}</div>
                  <div><span class="font-semibold">Background:</span> {selectedPersonaDetails.background}</div>
                  <div><span class="font-semibold">Session Context:</span> {selectedPersonaDetails.sessionContext}</div>
                </div>
              </div>
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-2">Conversational Style</h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-300">{selectedPersonaDetails.conversationalStyle}</p>
              </div>
            </div>
          </div>

          <!-- Three-Column Layout -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <!-- Left Column: History & Situation -->
            <div class="space-y-6">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  📋 Relevant History
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
                  {selectedPersonaDetails.relevantHistory}
                </p>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🎯 Current Situation
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
                  {selectedPersonaDetails.currentSituation}
                </p>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🎯 Presenting Concerns
                </h3>
                <div class="flex flex-wrap gap-2">
                  {#each selectedPersonaDetails.presentingConcerns as concern}
                    <span class="text-xs px-3 py-1 rounded-full bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800">
                      {concern}
                    </span>
                  {/each}
                </div>
              </div>
            </div>

            <!-- Middle Column: Cognitive Conceptualization -->
            <div class="space-y-6">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🧠 Core Beliefs
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.cognitiveConceptualization.coreBeliefs as belief}
                    <li class="flex items-start">
                      <span class="text-red-400 mr-2 mt-1">•</span>
                      <span class="italic">"{belief}"</span>
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🔗 Intermediate Beliefs
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefs as belief}
                    <li class="flex items-start">
                      <span class="text-orange-400 mr-2 mt-1">•</span>
                      <span class="italic">"{belief}"</span>
                    </li>
                  {/each}
                </ul>
              </div>

              {#if selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefsDepression}
                <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                  <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                    😔 Depression-Related Beliefs
                  </h3>
                  <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                    {#each selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefsDepression as belief}
                      <li class="flex items-start">
                        <span class="text-blue-400 mr-2 mt-1">•</span>
                        <span class="italic">"{belief}"</span>
                      </li>
                    {/each}
                  </ul>
                </div>
              {/if}

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🛠️ Coping Strategies
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.cognitiveConceptualization.copingStrategies as strategy}
                    <li class="flex items-start">
                      <span class="text-green-400 mr-2 mt-1">•</span>
                      {strategy}
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🛡️ Coping Mechanisms
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                  {#each selectedPersonaDetails.behaviors.copingMechanisms as mechanism}
                    <li class="flex items-start">
                      <span class="text-blue-400 mr-2 mt-1">•</span>
                      {mechanism}
                    </li>
                  {/each}
                </ul>
              </div>
            </div>

            <!-- Right Column: Emotions & Behaviors -->
            <div class="space-y-6">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  💭 Automatic Thoughts
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.automaticThoughts as thought}
                    <li class="flex items-start">
                      <span class="text-purple-400 mr-2 mt-1">•</span>
                      <span class="italic">"{thought}"</span>
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  😊 Emotions
                </h3>
                <div class="space-y-3">
                  <div>
                    <span class="text-xs font-medium text-neutral-700 dark:text-neutral-300">Primary:</span>
                    <div class="flex flex-wrap gap-1 mt-1">
                      {#each selectedPersonaDetails.emotions.primary as emotion}
                        <span class="text-xs px-2 py-1 rounded-full bg-neutral-200 dark:bg-neutral-600 text-neutral-700 dark:text-neutral-300">
                          {emotion}
                          {#if selectedPersonaDetails.emotions.intensityLevels?.[emotion]}
                            ({selectedPersonaDetails.emotions.intensityLevels[emotion]})
                          {/if}
                        </span>
                      {/each}
                    </div>
                  </div>
                  {#if selectedPersonaDetails.emotions.secondary}
                    <div>
                      <span class="text-xs font-medium text-neutral-700 dark:text-neutral-300">Secondary:</span>
                      <div class="flex flex-wrap gap-1 mt-1">
                        {#each selectedPersonaDetails.emotions.secondary as emotion}
                          <span class="text-xs px-2 py-1 rounded-full bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400">
                            {emotion}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}
                </div>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  ⚠️ Maladaptive Behaviors
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                  {#each selectedPersonaDetails.behaviors.maladaptive as behavior}
                    <li class="flex items-start">
                      <span class="text-red-400 mr-2 mt-1">•</span>
                      {behavior}
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🔄 Behavioral Patterns
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                  {#each selectedPersonaDetails.behaviors.behavioralPatterns as pattern}
                    <li class="flex items-start">
                      <span class="text-yellow-400 mr-2 mt-1">•</span>
                      {pattern}
                    </li>
                  {/each}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Main Content - Three Panes -->
  <div class="mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      
      <!-- Left Pane: Therapist Profile & Thinking -->
      <div class="card p-6 flex flex-col">
        <!-- Therapist Profile Section -->
        <div class="profile-section">
          <div class="profile-header">
            <!-- <div class="profile-avatar">
              {therapistProfile.name.split(' ').map(n => n[0]).join('')}
            </div> -->
            <div>
              <span class="text-3xl">👨‍⚕️</span>
              <span class="profile-name">{therapistProfile.name}</span>
              <!-- <p class="profile-title">{therapistProfile.credentials}</p> -->
            </div>
          </div>
          <!-- <div class="profile-details">
            <div class="profile-detail-item">
              <span class="profile-detail-label">Specialization:</span>
              <span class="profile-detail-value">{therapistProfile.specialization}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Approach:</span>
              <span class="profile-detail-value">{therapistProfile.approach}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Experience:</span>
              <span class="profile-detail-value">{therapistProfile.experience}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Sessions:</span>
              <span class="profile-detail-value">{therapistProfile.sessions}</span>
            </div>
          </div> -->
        </div>

        <!-- Therapist Thinking Section -->
        <div class="flex-1 flex flex-col">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
            Therapist Thinking
          </h2>
          <div class="space-y-3 flex-1 overflow-y-auto">
          {#each therapistThoughts as thought}
            <div class="thinking-therapist dark:bg-primary-900/20 dark:text-blue-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if therapistThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Therapist thinking will appear here during conversation...</p>
            </div>
          {/if}
          </div>
        </div>
      </div>

      <!-- Middle Pane: Conversation & Analytics -->
      <div class="card p-6 flex flex-col">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 flex items-center theme-transition">
            Conversation
          </h2>
          {#if currentEvaluation}
            <button
              on:click={() => showEvaluation = !showEvaluation}
              class="btn btn-secondary text-xs px-3 py-1"
              title={showEvaluation ? 'Show Analytics' : 'Show CBT Evaluation'}
            >
              {showEvaluation ? '📊 Analytics' : '📋 Evaluation'}
            </button>
          {/if}
        </div>
        <!-- Conversation Messages -->
        <div class="space-y-4 flex-1 overflow-y-auto">
          {#each messages as message}
            <div class="flex flex-col space-y-1">
              <div class="text-xs text-neutral-500 dark:text-neutral-400 flex items-center space-x-2 theme-transition">
                <span class="font-medium capitalize {message.sender === 'therapist' ? 'text-primary-600 dark:text-primary-400' : 'text-secondary-600 dark:text-secondary-400'} theme-transition">
                  {message.sender === 'therapist' ? '👨‍⚕️ Dr. Sila' : `👤 ${patientProfile.name}`}
                </span>
                <span>•</span>
                <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
              </div>
              <div class="message-bubble {message.sender === 'therapist' ? 'bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary-500' : 'bg-secondary-50 dark:bg-secondary-900/20 border-l-4 border-secondary-500'} text-neutral-900 dark:text-neutral-100 p-3 rounded-r-lg theme-transition">
                {#if message.metadata && message.metadata.patientAnalysis && message.sender === 'therapist'}
                  <!-- Patient Analysis Section -->
                  <div class="text-xs font-bold text-primary-600 dark:text-primary-400 mb-2">Patient Analysis:</div>

                  <div class="mb-2 space-y-1">
                    <!-- Basic Analysis -->
                    <div class="flex flex-wrap gap-1 items-center">
                      <span class="text-xs font-bold">Sentiment: </span>
                      <span class="text-xs font-bold px-2 py-1 rounded uppercase {
                          message.metadata.patientAnalysis.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                          message.metadata.patientAnalysis.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
                          'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                          } theme-transition">
                        {message.metadata.patientAnalysis.sentiment}
                      </span>

                      {#if message.metadata.patientAnalysis.sentimentIntensity}
                        <span class="text-xs text-gray-600 dark:text-gray-400">
                          ({message.metadata.patientAnalysis.sentimentIntensity} intensity)
                        </span>
                      {/if}
                    </div>

                    <div class="flex flex-wrap gap-1 items-center">
                      <span class="text-xs font-bold">Motivation: </span>
                      <span class="text-xs font-bold px-2 py-1 rounded uppercase {
                          message.metadata.patientAnalysis.motivationLevel === 'low' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                          message.metadata.patientAnalysis.motivationLevel === 'medium' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                          'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                          } theme-transition">
                        {message.metadata.patientAnalysis.motivationLevel}
                      </span>

                      {#if message.metadata.patientAnalysis.motivationType}
                        <span class="text-xs text-gray-600 dark:text-gray-400">
                          ({message.metadata.patientAnalysis.motivationType})
                        </span>
                      {/if}
                    </div>

                    <div class="flex flex-wrap gap-1 items-center">
                      <span class="text-xs font-bold">Engagement: </span>
                      <span class="text-xs font-bold px-2 py-1 rounded uppercase {
                          message.metadata.patientAnalysis.engagementLevel === 'low' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                          message.metadata.patientAnalysis.engagementLevel === 'medium' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                          'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                          } theme-transition">
                        {message.metadata.patientAnalysis.engagementLevel}
                      </span>
                    </div>

                    <!-- Engagement Patterns -->
                    {#if message.metadata.patientAnalysis.engagementPatterns && message.metadata.patientAnalysis.engagementPatterns.length > 0}
                      <div class="flex flex-wrap gap-1 items-center">
                        <span class="text-xs font-bold">Patterns: </span>
                        {#each message.metadata.patientAnalysis.engagementPatterns as pattern}
                          <span class="text-xs px-1 py-0.5 rounded bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300">
                            {pattern}
                          </span>
                        {/each}
                      </div>
                    {/if}
                  </div>

                  <!-- Readiness Score Section -->
                  {#if message.metadata.patientAnalysis.readinessScore}
                    <div class="mb-2 space-y-1">
                      <div class="flex items-center gap-2">
                        <span class="text-xs font-bold">Readiness Score: </span>
                        <span class="text-xs font-bold px-2 py-1 rounded {
                          message.metadata.patientAnalysis.readinessScore.score >= 8 ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                          message.metadata.patientAnalysis.readinessScore.score >= 5 ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                          'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                          } theme-transition">
                          {message.metadata.patientAnalysis.readinessScore.score}/10
                        </span>
                        <span class="text-xs text-gray-600 dark:text-gray-400">
                          ({message.metadata.patientAnalysis.readinessScore.recommendedApproach})
                        </span>
                      </div>

                      <!-- Readiness Indicators -->
                      {#if message.metadata.patientAnalysis.readinessScore.indicators}
                        {#if message.metadata.patientAnalysis.readinessScore.indicators.positive.length > 0}
                          <div class="flex flex-wrap gap-1 items-center">
                            <span class="text-xs font-bold text-green-600 dark:text-green-400">Positive:</span>
                            {#each message.metadata.patientAnalysis.readinessScore.indicators.positive as indicator}
                              <span class="text-xs px-1 py-0.5 rounded bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300">
                                {indicator}
                              </span>
                            {/each}
                          </div>
                        {/if}

                        {#if message.metadata.patientAnalysis.readinessScore.indicators.negative.length > 0}
                          <div class="flex flex-wrap gap-1 items-center">
                            <span class="text-xs font-bold text-red-600 dark:text-red-400">Areas for Focus:</span>
                            {#each message.metadata.patientAnalysis.readinessScore.indicators.negative as indicator}
                              <span class="text-xs px-1 py-0.5 rounded bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300">
                                {indicator}
                              </span>
                            {/each}
                          </div>
                        {/if}
                      {/if}

                      <!-- Reasoning -->
                      {#if message.metadata.patientAnalysis.readinessScore.reasoning}
                        <div class="text-xs text-gray-600 dark:text-gray-400 italic">
                          {message.metadata.patientAnalysis.readinessScore.reasoning}
                        </div>
                      {/if}
                    </div>
                  {/if}

                  <!-- Therapeutic Approach Section -->
                  {#if message.metadata.therapeuticApproach}
                    <div class="text-xs font-bold text-primary-600 dark:text-primary-400 mb-1">Therapeutic Approach:</div>
                    <div class="mb-2">
                      <span class="text-xs font-bold px-2 py-1 rounded bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 theme-transition">
                        {message.metadata.therapeuticApproach.name}
                      </span>
                    </div>

                    <div class="text-xs font-bold text-primary-600 dark:text-primary-400 mb-1">Selected Technique:</div>
                    <div class="mb-2">
                      <span class="text-xs font-bold px-2 py-1 rounded bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 theme-transition">
                        {message.metadata.therapeuticApproach.selectedTechnique.name}
                      </span>
                      <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {message.metadata.therapeuticApproach.selectedTechnique.description}
                      </div>
                    </div>
                  {/if}

                  <!-- Divider -->
                  <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
                {/if}
                {message.content}
              </div>
            </div>
          {/each}

          <!-- Show spinner while conversationActive = true -->
          {#if conversationActive}
            <div role="status" class="text-center">
              <svg aria-hidden="true" class="inline w-6 h-6 text-neutral-300 dark:text-neutral-600 animate-spin fill-primary-600 dark:fill-primary-400 theme-transition" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                  <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
              </svg>
              <span class="sr-only">Loading...</span>
            </div>
          {:else if messages.length === 0}
            <div class="flex items-center justify-center h-full text-sm text-neutral-500 dark:text-neutral-400 theme-transition">
              <p>Click "Start" to begin...</p>
            </div>
          {/if}
        </div>

        <!-- CBT Evaluation or Client Analytics Section -->
        {#if showEvaluation}
          <!-- CBT Evaluation Display -->
          <div class="evaluation-section">
            <EvaluationDisplay
              evaluation={currentEvaluation}
              isLoading={evaluationLoading}
              error={evaluationError}
            />
          </div>
        {:else}
          <!-- Client Analytics Section -->
          <div class="analytics-section">
            <div class="analytics-header">
              Client State Analytics
            </div>
            {#if preConversationAnalytics.sentiment || postConversationAnalytics.sentiment}
              <div class="analytics-grid">
                <!-- Pre-Conversation Column -->
                <div class="analytics-column">
                  <div class="analytics-column-title">Pre-Conversation</div>
                  <div class="analytics-metric">
                    <span class="analytics-metric-label">Sentiment:</span>
                    <span class="analytics-metric-value {preConversationAnalytics.sentiment || 'neutral'}">
                      {preConversationAnalytics.sentiment || 'N/A'}
                    </span>
                  </div>
                  <div class="analytics-metric">
                    <span class="analytics-metric-label">Motivation:</span>
                    <span class="analytics-metric-value {preConversationAnalytics.motivationLevel || 'neutral'}">
                      {preConversationAnalytics.motivationLevel || 'N/A'}
                    </span>
                  </div>
                  <div class="analytics-metric">
                    <span class="analytics-metric-label">Engagement:</span>
                    <span class="analytics-metric-value {preConversationAnalytics.engagementLevel || 'neutral'}">
                      {preConversationAnalytics.engagementLevel || 'N/A'}
                    </span>
                  </div>
                </div>

                <!-- Post-Conversation Column -->
                <div class="analytics-column">
                  <div class="analytics-column-title">Current State</div>
                  <div class="analytics-metric">
                    <span class="analytics-metric-label">Sentiment:</span>
                    <span class="analytics-metric-value {postConversationAnalytics.sentiment || 'neutral'}">
                      {postConversationAnalytics.sentiment || 'N/A'}
                    </span>
                  </div>
                  <div class="analytics-metric">
                    <span class="analytics-metric-label">Motivation:</span>
                    <span class="analytics-metric-value {postConversationAnalytics.motivationLevel || 'neutral'}">
                      {postConversationAnalytics.motivationLevel || 'N/A'}
                    </span>
                  </div>
                  <div class="analytics-metric">
                    <span class="analytics-metric-label">Engagement:</span>
                    <span class="analytics-metric-value {postConversationAnalytics.engagementLevel || 'neutral'}">
                      {postConversationAnalytics.engagementLevel || 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            {:else}
              <div class="analytics-no-data">
                Analytics will appear here during conversation...
              </div>
            {/if}
          </div>
        {/if}
      </div>

      <!-- Right Pane: Patient Profile & Thinking -->
      <div class="card p-6 flex flex-col">
        <!-- Patient Profile Section -->
        <div class="profile-section">
          <div class="profile-header">
            <!-- <div class="profile-avatar patient">
              {patientProfile.name.split(' ').map(n => n[0]).join('')}
            </div> -->
            <div class="flex-1">
              <span class="text-2xl">😵‍💫</span>
              <span class="profile-name">{patientProfile.name}</span>
            </div>
            {#if selectedPersonaDetails}
              <button
                on:click={() => showPatientDetailsModal = true}
                class="btn btn-secondary text-xs px-3 py-1 ml-2"
                title="View detailed patient information"
              >
                Details
              </button>
            {/if}
          </div>
          <!-- <div class="profile-details">
            <div class="profile-detail-item">
              <span class="profile-detail-label">Session:</span>
              <span class="profile-detail-value">{patientProfile.sessionHistory}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Focus Area:</span>
              <span class="profile-detail-value">{patientProfile.background}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Traits:</span>
              <span class="profile-detail-value">{patientProfile.traits}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Last Session:</span>
              <span class="profile-detail-value">{patientProfile.lastSession}</span>
            </div>
          </div> -->
        </div>

        <!-- Patient Thinking Section -->
        <div class="flex-1 flex flex-col">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
            Patient Thinking
          </h2>
          <div class="space-y-3 flex-1 overflow-y-auto">
          {#each patientThoughts as thought}
            <div class="thinking-patient dark:bg-secondary-900/20 dark:text-purple-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if patientThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Patient thinking will appear here during conversation...</p>
            </div>
          {/if}
          </div>
        </div>
      </div>
      
    </div>
  </div>
</div>

<style>
  .evaluation-section {
    @apply border-t border-neutral-200 dark:border-neutral-700 pt-4 mt-4 flex-1 overflow-hidden;
  }
</style>
