<script lang="ts">
  import type { CBTEvaluationResult } from '../types/index.js';

  export let evaluation: CBTEvaluationResult | null = null;
  export let isLoading: boolean = false;
  export let error: string | null = null;

  // Helper function to get score color
  function getScoreColor(score: number): string {
    if (score >= 5) return 'text-green-600 dark:text-green-400';
    if (score >= 4) return 'text-blue-600 dark:text-blue-400';
    if (score >= 2) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  }

  // Helper function to get score background
  function getScoreBackground(score: number): string {
    if (score >= 5) return 'bg-green-100 dark:bg-green-900/20';
    if (score >= 4) return 'bg-blue-100 dark:bg-blue-900/20';
    if (score >= 2) return 'bg-yellow-100 dark:bg-yellow-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  }

  // Helper function to get assessment color
  function getAssessmentColor(assessment: string): string {
    switch (assessment) {
      case 'excellent': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20';
      case 'good': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20';
      case 'fair': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20';
      case 'poor': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20';
      default: return 'text-neutral-600 dark:text-neutral-400 bg-neutral-100 dark:bg-neutral-900/20';
    }
  }

  // Helper function to format dimension name
  function formatDimensionName(key: string): string {
    const names: Record<string, string> = {
      cbtValidity: 'CBT Validity',
      cbtAppropriateness: 'CBT Appropriateness',
      cbtAccuracy: 'CBT Accuracy',
      esAppropriateness: 'ES Appropriateness',
      stability: 'Stability'
    };
    return names[key] || key;
  }
</script>

<div class="evaluation-container">
  {#if isLoading}
    <!-- Loading State -->
    <div class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      <span class="ml-3 text-neutral-600 dark:text-neutral-400">Evaluating CBT effectiveness...</span>
    </div>
  {:else if error}
    <!-- Error State -->
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex items-center">
        <span class="text-red-600 dark:text-red-400 text-xl mr-2">⚠️</span>
        <div>
          <h3 class="text-red-800 dark:text-red-200 font-medium">Evaluation Error</h3>
          <p class="text-red-700 dark:text-red-300 text-sm mt-1">{error}</p>
        </div>
      </div>
    </div>
  {:else if evaluation}
    <!-- Evaluation Results -->
    <div class="space-y-6">
      <!-- Overall Assessment Header -->
      <div class="text-center">
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          CBT Effectiveness Evaluation
        </h3>
        <div class="flex items-center justify-center space-x-4">
          <div class="text-3xl font-bold {getScoreColor(evaluation.overallScore)}">
            {evaluation.overallScore.toFixed(1)}/6.0
          </div>
          <div class="px-3 py-1 rounded-full text-sm font-medium {getAssessmentColor(evaluation.overallAssessment)}">
            {evaluation.overallAssessment.toUpperCase()}
          </div>
        </div>
      </div>

      <!-- Dimension Scores -->
      <div class="space-y-3">
        <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-3">Evaluation Dimensions</h4>
        {#each Object.entries(evaluation.dimensions) as [key, dimension]}
          <div class="dimension-card p-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800">
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium text-neutral-900 dark:text-neutral-100">
                {formatDimensionName(key)}
              </span>
              <div class="flex items-center space-x-2">
                <span class="text-sm px-2 py-1 rounded {getScoreBackground(dimension.score)} {getScoreColor(dimension.score)} font-medium">
                  {dimension.score}/6
                </span>
              </div>
            </div>
            <p class="text-sm text-neutral-600 dark:text-neutral-400 mb-2">
              {dimension.criteria}
            </p>
            {#if dimension.rationale}
              <p class="text-xs text-neutral-500 dark:text-neutral-500 italic">
                {dimension.rationale}
              </p>
            {/if}
          </div>
        {/each}
      </div>

      <!-- Session Summary -->
      {#if evaluation.conversationSummary}
        <div class="summary-section">
          <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2">Session Summary</h4>
          <div class="p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700">
            <p class="text-sm text-neutral-700 dark:text-neutral-300">
              {evaluation.conversationSummary}
            </p>
          </div>
        </div>
      {/if}

      <!-- Recommendations -->
      {#if evaluation.recommendations && evaluation.recommendations.length > 0}
        <div class="recommendations-section">
          <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2">Recommendations</h4>
          <div class="space-y-2">
            {#each evaluation.recommendations as recommendation}
              <div class="flex items-start space-x-2 p-2 rounded bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                <span class="text-blue-600 dark:text-blue-400 text-sm mt-0.5">💡</span>
                <p class="text-sm text-blue-800 dark:text-blue-200">{recommendation}</p>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Metadata -->
      <div class="metadata-section">
        <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2">Session Details</h4>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div class="flex justify-between">
            <span class="text-neutral-600 dark:text-neutral-400">Messages:</span>
            <span class="text-neutral-900 dark:text-neutral-100">{evaluation.metadata.totalMessages}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-neutral-600 dark:text-neutral-400">Duration:</span>
            <span class="text-neutral-900 dark:text-neutral-100">{evaluation.metadata.sessionDuration} min</span>
          </div>
          <div class="flex justify-between">
            <span class="text-neutral-600 dark:text-neutral-400">Evaluated:</span>
            <span class="text-neutral-900 dark:text-neutral-100">
              {new Date(evaluation.evaluationTimestamp).toLocaleTimeString()}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-neutral-600 dark:text-neutral-400">Model:</span>
            <span class="text-neutral-900 dark:text-neutral-100">{evaluation.metadata.evaluationModel}</span>
          </div>
        </div>
      </div>
    </div>
  {:else}
    <!-- No Evaluation State -->
    <div class="text-center py-8">
      <div class="text-neutral-400 dark:text-neutral-500 text-4xl mb-3">📊</div>
      <h3 class="text-neutral-600 dark:text-neutral-400 font-medium mb-2">No Evaluation Available</h3>
      <p class="text-neutral-500 dark:text-neutral-500 text-sm">
        CBT evaluation will appear here after the therapy session is completed.
      </p>
    </div>
  {/if}
</div>

<style>
  .evaluation-container {
    @apply h-full overflow-y-auto;
  }

  .dimension-card {
    @apply transition-all duration-200 hover:shadow-sm;
  }

  .summary-section,
  .recommendations-section,
  .metadata-section {
    @apply border-t border-neutral-200 dark:border-neutral-700 pt-4;
  }
</style>
